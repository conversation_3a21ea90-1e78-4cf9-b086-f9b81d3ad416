import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { createDateFilter } from '../../utils/util';
import { OrderType, PaymentType } from '../../utils/util';
import {
  staffHasPermission,
  PERMISSIONS,
  createStaffAuthHelper,
} from '../../utils/permission';import { formatString } from '../../utils/stringFormatter';


export const orderService = {
checkStaffPayment: async (staffId: any, reqBody: any) => {
  const auth = createStaffAuthHelper(staffId);

  const canManage = await auth.hasPermission(PERMISSIONS.CAFETERIA_POS_ACCESS);
  if (!canManage) {
    throw new HttpError('Unauthorized', 403);
  }

  const { code, amount } = reqBody;
  const staffCode = formatString.trimString(code);

  const checkStaff = await db.staff.findUnique({
    where: { staffCode },
    include: { location: true }, 
  });

  if (!checkStaff || !checkStaff.isActive) {
    throw new HttpError('Staff does not exist', 400);
  }
      if (checkStaff.locked) {
    throw new HttpError('Staff account is locked', 403);
  }

  const requesterLocationId = await auth.getLocationId();

  const requesterLocation = await db.location.findUnique({
    where: { id: Number(requesterLocationId) },
  });

  const sameGroup = requesterLocation?.regionId === checkStaff.location.regionId;

  if (!sameGroup) {
    throw new HttpError('You are not authorized to access this cafeteria', 403);
  }

  const isCreditSufficient = Number(checkStaff.creditLimit) >= amount;
  const isWalletSufficient = Number(checkStaff.wallet) >= amount;

  return {
    creditLimit: isCreditSufficient,
    wallet: isWalletSufficient,
  };
},
createOrder: async (staffId: any, reqBody: any) => {
  const { cart, code, type, method, totalAmount, ...order } = reqBody;
  const auth = createStaffAuthHelper(staffId);

  const canManage = await auth.hasPermission(PERMISSIONS.CAFETERIA_POS_ACCESS);
  if (!canManage) {
    throw new HttpError('Unauthorized', 403);
  }

  const requesterLocationId = await auth.getLocationId();

  const requesterLocation = await db.location.findUnique({
    where: { id: Number(requesterLocationId) },
  });

  if (!requesterLocation) {
    throw new HttpError('Invalid location', 400);
  }

  if (!PaymentType.includes(method.toUpperCase())) {
    throw new HttpError('Not an acceptable payment method', 403);
  }

  if (!OrderType.includes(type.toUpperCase())) {
    throw new HttpError('Not an acceptable order type', 403);
  }

  const orderNumber = `CAF-${requesterLocation.name}-${Date.now()}`;
  const operations: any[] = [];

  let staffCode = null;

  if (code) {
    staffCode = formatString.trimString(code);

    const staffRecord = await db.staff.findUnique({
      where: { staffCode },
      include: { location: true },
    });

    if (!staffRecord || !staffRecord.isActive) {
      throw new HttpError('Staff does not exist', 400);
    }

    const sameGroup = requesterLocation.regionId === staffRecord.location.regionId;
    if (!sameGroup) {
      throw new HttpError('You are not authorized to access this cafeteria', 403);
    }

    const methodUpper = method.toUpperCase();

    if (methodUpper === 'CREDIT') {
      operations.push(
        db.staff.update({
          where: { staffCode },
          data: {
            creditLimit: {
              decrement: Number(totalAmount),
            },
          },
        })
      );
    }

    if (methodUpper === 'WALLET') {
      operations.push(
        db.staff.update({
          where: { staffCode },
          data: {
            wallet: {
              decrement: Number(totalAmount),
            },
          },
        })
      );
    }
  }

  const orderCreation = db.cafeteriaOrder.create({
    data: {
      ...order,
      orderNumber,
      locationId: Number(requesterLocationId),
      staffCode: staffCode || null,
      orderItems: {
        create: cart.map((item: any) => ({
          menuItemId: Number(item.id),
          quantity: Number(item.quantity),
          unitPrice: Number(item.price),
          totalPrice: Number(item.quantity) * Number(item.price),
        })),
      },
    },
  });

  operations.push(orderCreation);

  // Execute all operations transactionally
  const results = await db.$transaction(operations);
  const createdOrder = results.at(-1); // last item is the order

  return createdOrder;
},


  // createOrder: async (staffId: any, reqBody: any) => {
  //   const { cart, code, type,method, totalAmount, ...order } = reqBody;
  // const auth = createStaffAuthHelper(staffId);

  // const canManage = await auth.hasPermission(PERMISSIONS.CAFETERIA_POS_ACCESS);
  // if (!canManage) {
  //   throw new HttpError('Unauthorized', 403);
  // }

  // const staffCode = formatString.trimString(code);

  // const checkStaff = await db.staff.findUnique({
  //   where: { staffCode },
  //   include: { location: true }, 
  // });

  // if (!checkStaff || !checkStaff.isActive) {
  //   throw new HttpError('Staff does not exist', 400);
  // }

  // const requesterLocationId = await auth.getLocationId();

  // const requesterLocation = await db.location.findUnique({
  //   where: { id: Number(requesterLocationId) },
  // });

  // const sameGroup = requesterLocation?.regionId === checkStaff.location.regionId;

  // if (!sameGroup) {
  //   throw new HttpError('You are not authorized to access this cafeteria', 403);
  // }

  //     if (!PaymentType.includes(method.toUpperCase())) {
  //    throw new HttpError('Not an acceptable payment method', 403);
  //   }
  //     if (!OrderType.includes(type.toUpperCase())) {
  //    throw new HttpError('Not an acceptable order type', 403);
  //   }
  //   const orderNumber = `CAF-${requesterLocation.name}-${Date.now()}`;

  //   return db.cafeteriaOrder.create({
  //     data: {
  //       ...order,
  //       orderNumber,
  //       locationId: Number(requesterLocationId),
  //       orderItems: {
  //         create: cart.map((item: any) => ({
  //           menuItemId: Number(item.id),
  //           quantity: Number(item.quantity),
  //           unitPrice: Number(item.price),
  //           totalPrice: Number(item.quantity) * Number(item.price),
  //         })),
  //       },
  //     },
  //   });
  // },

  getOrderById: async (staffId: any, id: string) => {
    await staffHasPermission(staffId, PERMISSIONS.CAFETERIA_ORDERS_MANAGE);
    const order = await db.cafeteriaOrder.findUnique({
      where: { id },
      include: {
        orderItems: {
          include: {
            menuItem: true,
          },
        },
        staff: true,
      },
    });
    if (!order) {
      throw new HttpError('Order not found', 404);
    }
    return order;
  },

  getAllOrders: async (staffId: any, query: any = {}) => {
    await staffHasPermission(staffId, PERMISSIONS.CAFETERIA_ORDERS_MANAGE);

    const page: number = parseInt(query.page as string) || 1;
    const limit: number = parseInt(query.limit as string) || 10;
    const search: string = (query.search as string) || '';
    const startDate = query.startDate as string;
    const payment = query.payment;
    const type = query.type;
    const staff = query.staff;
    const endDate = query.endDate as string;
    const dateFilter = createDateFilter(startDate, endDate);

    const whereClause: any = {
      ...(search
        ? {
            OR: [
              { patientRoomNo: { contains: search, mode: 'insensitive' } },
              { customerName: { contains: search, mode: 'insensitive' } },
              { customerPhone: { contains: search, mode: 'insensitive' } },
            ],
          }
        : {}),
      ...(payment ? { paymentType: payment.toUpperCase() } : {}),
      ...(type ? { orderType: type.toUpperCase() } : {}),
      ...(staff ? { staffId: Number(staff) } : {}),
      ...dateFilter,
    };

    const [orders, totalCount] = await db.$transaction([
      db.cafeteriaOrder.findMany({
        where: whereClause,
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          orderItems: {
            include: {
              menuItem: true,
            },
          },
        },
      }),
      db.cafeteriaOrder.count({
        where: whereClause,
      }),
    ]);

    const response = {
      orders: orders,
      totalPages: Math.ceil(totalCount / limit),
      totalCount: totalCount,
      currentPage: page,
      limit: limit,
    };

    return response;
  },
};
